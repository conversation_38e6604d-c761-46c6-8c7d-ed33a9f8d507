[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "memect-insight-extractor"
version = "0.1.0"
description = "大模型文档信息提取模块"
authors = [
    {name = "Memect Team"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "openai>=1.0.0",
    "pydantic>=2.0.0",
    "jsonschema>=4.0.0",
    "aiohttp>=3.8.0",
    "tenacity>=8.0.0",
    "loguru>=0.7.0",
    "tokenizers>=0.15.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "devpi-client>=3.0.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.0.0",
]

[tool.hatch.build.targets.wheel]
packages = ["src/memect_insight_extractor"]

[tool.hatch.build.targets.wheel.force-include]
"src/memect_insight_extractor/tokenizers" = "memect_insight_extractor/tokenizers"

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v"
