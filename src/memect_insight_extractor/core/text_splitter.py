"""文本分段处理模块"""

import re
from typing import Any, List, Optional, Tuple, Union

from ..config import ExtractionConfig
from ..utils.logger import get_logger
from ..utils.tokenizer_manager import get_tokenizer_manager

logger = get_logger(__name__)


class TextChunk:
    """文本块类"""

    def __init__(
        self,
        content: str,
        start_pos: int,
        end_pos: int,
        chunk_index: int,
        token_count: Optional[int] = None
    ):
        """初始化文本块

        Args:
            content: 文本内容
            start_pos: 起始位置
            end_pos: 结束位置
            chunk_index: 块索引
            token_count: token数量
        """
        self.content = content
        self.start_pos = start_pos
        self.end_pos = end_pos
        self.chunk_index = chunk_index
        self.token_count = token_count

    def __len__(self) -> int:
        """返回文本块长度"""
        return len(self.content)

    def __str__(self) -> str:
        """返回文本块内容"""
        return self.content


class TextSplitter:
    """文本分段处理器

    负责将超长文档按章节或逻辑块切分，支持基于token数的智能分割
    """

    def __init__(self, config: ExtractionConfig):
        """初始化文本分段器

        Args:
            config: 提取配置
        """
        self.config = config
        self.tokenizer_manager = None
        self._init_tokenizer()

        # 章节标题模式（增强markdown支持）
        self.section_patterns = [
            r"^#{1,6}\s+(.+)$",  # Markdown标题 H1-H6
            r"^(.+)\n[=]{3,}$",  # Markdown H1 下划线格式
            r"^(.+)\n[-]{3,}$",  # Markdown H2 下划线格式
            r"^第[一二三四五六七八九十\d]+章\s*[：:：]?\s*(.+)$",  # 第X章
            r"^第[一二三四五六七八九十\d]+节\s*[：:：]?\s*(.+)$",  # 第X节
            r"^[一二三四五六七八九十\d]+[、\.]\s*(.+)$",  # 1. 或 一、
            r"^[（\(][一二三四五六七八九十\d]+[）\)]\s*(.+)$",  # (1) 或 （一）
            r"^\d+\.\d+\s+(.+)$",  # 1.1
        ]

        # Markdown特殊块模式
        self.markdown_block_patterns = [
            r"^```[\s\S]*?^```$",  # 代码块
            r"^\|.*\|$",  # 表格行
            r"^>\s+",  # 引用块
        ]

    def _init_tokenizer(self) -> None:
        """初始化tokenizer"""
        try:
            self.tokenizer_manager = get_tokenizer_manager()

            # 检查本地tokenizer是否可用
            if self.tokenizer_manager.is_available(self.config.tokenizer_type):
                logger.info(f"使用本地 {self.config.tokenizer_type} tokenizer")
            else:
                logger.warning(f"本地 {self.config.tokenizer_type} tokenizer不可用，将使用字符数估算")

        except Exception as e:
            logger.warning(f"tokenizer初始化失败: {e}，将使用字符数估算")
            self.tokenizer_manager = None

    def count_tokens(self, text: str) -> int:
        """计算文本的token数量

        Args:
            text: 文本内容

        Returns:
            token数量
        """
        if self.tokenizer_manager is None:
            # 回退到字符数估算
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

        try:
            return self.tokenizer_manager.count_tokens(
                text,
                self.config.tokenizer_type,
                self.config.llm_model
            )
        except Exception as e:
            logger.warning(f"token计数失败: {e}，使用字符数估算")
            chinese_chars = sum(1 for char in text if "\u4e00" <= char <= "\u9fff")
            other_chars = len(text) - chinese_chars
            return chinese_chars + max(1, other_chars // 4)

    def should_split(self, text: str) -> bool:
        """判断是否需要分段

        Args:
            text: 文本内容

        Returns:
            是否需要分段
        """
        # 基于token数判断
        token_count = self.count_tokens(text)
        return token_count > self.config.max_chunk_tokens

    def split_text(self, text: str) -> List[TextChunk]:
        """分割文本

        Args:
            text: 原始文本

        Returns:
            文本块列表
        """
        if not self.should_split(text):
            token_count = self.count_tokens(text)
            return [TextChunk(text, 0, len(text), 0, token_count)]

        token_count = self.count_tokens(text)
        logger.info(
            f"文本长度 {len(text)} 字符，{token_count} tokens，"
            f"超过限制（{self.config.max_chunk_tokens} tokens），开始分段"
        )

        # 尝试按章节分割
        chunks = self._split_by_sections(text)

        # 智能合并小块和分割大块
        final_chunks = self._optimize_chunks(chunks)

        # 重新编号并确保token计数
        for i, chunk in enumerate(final_chunks):
            chunk.chunk_index = i
            if chunk.token_count is None:
                chunk.token_count = self.count_tokens(chunk.content)

        total_tokens = sum(chunk.token_count or 0 for chunk in final_chunks)
        logger.info(f"文本分割完成，共 {len(final_chunks)} 个块，总计 {total_tokens} tokens")
        return final_chunks

    def _optimize_chunks(self, chunks: List[TextChunk]) -> List[TextChunk]:
        """优化块列表：合并小块，分割大块

        Args:
            chunks: 初始块列表

        Returns:
            优化后的块列表
        """
        if not chunks:
            return chunks

        optimized_chunks = []
        current_chunk = None
        target_tokens = int(self.config.max_chunk_tokens * 0.8)  # 目标80%利用率
        min_tokens = int(self.config.max_chunk_tokens * 0.3)     # 最小30%利用率

        for chunk in chunks:
            chunk_tokens = chunk.token_count or self.count_tokens(chunk.content)

            # 如果块太大，需要分割
            if chunk_tokens > self.config.max_chunk_tokens:
                # 先保存当前累积的块
                if current_chunk:
                    optimized_chunks.append(current_chunk)
                    current_chunk = None

                # 分割大块
                sub_chunks = self._split_large_chunk(chunk)
                optimized_chunks.extend(sub_chunks)
                continue

            # 如果没有当前块，开始新块
            if current_chunk is None:
                current_chunk = chunk
                continue

            # 尝试合并到当前块
            current_tokens = current_chunk.token_count or self.count_tokens(current_chunk.content)
            combined_content = current_chunk.content + "\n\n" + chunk.content
            combined_tokens = self.count_tokens(combined_content)

            # 如果合并后不超过限制，进行合并
            if combined_tokens <= self.config.max_chunk_tokens:
                # 创建合并后的块
                current_chunk = TextChunk(
                    content=combined_content,
                    start_pos=current_chunk.start_pos,
                    end_pos=chunk.end_pos,
                    chunk_index=current_chunk.chunk_index,
                    token_count=combined_tokens
                )
            else:
                # 无法合并，保存当前块并开始新块
                # 但是如果当前块太小，尝试强制合并（即使稍微超过限制）
                if (current_tokens < min_tokens and
                    combined_tokens <= self.config.max_chunk_tokens * 1.1):  # 允许10%超出
                    current_chunk = TextChunk(
                        content=combined_content,
                        start_pos=current_chunk.start_pos,
                        end_pos=chunk.end_pos,
                        chunk_index=current_chunk.chunk_index,
                        token_count=combined_tokens
                    )
                else:
                    optimized_chunks.append(current_chunk)
                    current_chunk = chunk

        # 添加最后一个块
        if current_chunk:
            optimized_chunks.append(current_chunk)

        logger.debug(f"块优化：{len(chunks)} -> {len(optimized_chunks)} 个块")
        return optimized_chunks

    def _split_by_sections(self, text: str) -> List[TextChunk]:
        """按章节分割文本，支持markdown格式

        Args:
            text: 原始文本

        Returns:
            文本块列表
        """
        lines = text.split("\n")
        sections = []
        current_section = []
        current_start = 0
        in_code_block = False
        in_table = False

        i = 0
        while i < len(lines):
            line = lines[i]
            original_line = line
            line_stripped = line.strip()

            # 检查代码块状态
            if line_stripped.startswith("```"):
                in_code_block = not in_code_block
                current_section.append(original_line)
                i += 1
                continue

            # 在代码块内，不进行章节分割
            if in_code_block:
                current_section.append(original_line)
                i += 1
                continue

            # 检查表格状态
            if "|" in line_stripped and line_stripped.startswith("|"):
                in_table = True
            elif in_table and not ("|" in line_stripped):
                in_table = False

            # 检查markdown下划线标题格式
            is_underline_title = False
            if i + 1 < len(lines) and not in_table:
                next_line = lines[i + 1].strip()
                if (next_line and
                    (all(c == "=" for c in next_line) or all(c == "-" for c in next_line)) and
                    len(next_line) >= 3):
                    is_underline_title = True

            # 检查是否是章节标题
            is_section_title = (not in_table and
                               (self._is_section_title(line_stripped) or is_underline_title))

            if is_section_title and current_section:
                # 保存当前章节
                section_content = "\n".join(current_section)
                section_end = current_start + len(section_content)
                token_count = self.count_tokens(section_content)
                sections.append(
                    TextChunk(
                        content=section_content,
                        start_pos=current_start,
                        end_pos=section_end,
                        chunk_index=len(sections),
                        token_count=token_count
                    )
                )

                # 开始新章节
                current_section = [original_line]
                current_start = section_end + 1

                # 如果是下划线标题，包含下一行
                if is_underline_title:
                    i += 1
                    if i < len(lines):
                        current_section.append(lines[i])
            else:
                current_section.append(original_line)

            i += 1

        # 添加最后一个章节
        if current_section:
            section_content = "\n".join(current_section)
            section_end = current_start + len(section_content)
            token_count = self.count_tokens(section_content)
            sections.append(
                TextChunk(
                    content=section_content,
                    start_pos=current_start,
                    end_pos=section_end,
                    chunk_index=len(sections),
                    token_count=token_count
                )
            )

        # 如果没有找到章节，返回整个文本
        if not sections:
            token_count = self.count_tokens(text)
            sections = [TextChunk(text, 0, len(text), 0, token_count)]

        total_tokens = sum(s.token_count or 0 for s in sections)
        logger.debug(f"按章节分割得到 {len(sections)} 个段落，总计 {total_tokens} tokens")
        return sections

    def _is_section_title(self, line: str) -> bool:
        """判断是否是章节标题

        Args:
            line: 文本行

        Returns:
            是否是章节标题
        """
        if not line or len(line) > 100:  # 标题通常不会太长
            return False

        for pattern in self.section_patterns:
            if re.match(pattern, line, re.MULTILINE):
                return True

        return False

    def _split_large_chunk(self, chunk: TextChunk) -> List[TextChunk]:
        """分割超大文本块，简单策略：一段一段添加，超过限制就去掉最后一段

        Args:
            chunk: 超大文本块

        Returns:
            分割后的文本块列表
        """
        text = chunk.content
        chunks = []

        # 如果文本没有段落分隔符，按token长度分割
        if "\n\n" not in text:
            return self._split_by_tokens(text, chunk.start_pos)

        # 按段落分割，简单策略
        paragraphs = text.split("\n\n")
        current_paragraphs = []
        current_start = chunk.start_pos

        for paragraph in paragraphs:
            # 尝试添加当前段落
            test_paragraphs = current_paragraphs + [paragraph]
            test_content = "\n\n".join(test_paragraphs)
            test_tokens = self.count_tokens(test_content)

            # 如果添加后超过限制
            if test_tokens > self.config.max_chunk_tokens:
                # 如果当前已有段落，保存当前块（去掉最后一段）
                if current_paragraphs:
                    current_content = "\n\n".join(current_paragraphs)
                    chunk_end = current_start + len(current_content)
                    token_count = self.count_tokens(current_content)
                    chunks.append(
                        TextChunk(
                            content=current_content,
                            start_pos=current_start,
                            end_pos=chunk_end,
                            chunk_index=len(chunks),
                            token_count=token_count
                        )
                    )

                    # 开始新块，从当前段落开始
                    current_paragraphs = [paragraph]
                    current_start = chunk_end + 2  # +2 for \n\n
                else:
                    # 如果单个段落就超过限制，强制添加（避免无限循环）
                    current_paragraphs = [paragraph]
            else:
                # 可以安全添加
                current_paragraphs = test_paragraphs

        # 添加最后一个块
        if current_paragraphs:
            current_content = "\n\n".join(current_paragraphs)
            chunk_end = current_start + len(current_content)
            token_count = self.count_tokens(current_content)
            chunks.append(
                TextChunk(
                    content=current_content,
                    start_pos=current_start,
                    end_pos=chunk_end,
                    chunk_index=len(chunks),
                    token_count=token_count
                )
            )

        total_tokens = sum(c.token_count or 0 for c in chunks)
        logger.debug(f"超大块分割为 {len(chunks)} 个子块，总计 {total_tokens} tokens")
        return chunks



    def _split_by_tokens(self, text: str, start_pos: int) -> List[TextChunk]:
        """按token数分割文本，尽可能保持较大片段

        Args:
            text: 文本内容
            start_pos: 起始位置

        Returns:
            文本块列表
        """
        chunks = []
        current_pos = 0
        chunk_index = 0

        while current_pos < len(text):
            # 估算结束位置（基于token数）
            estimated_char_per_token = len(text) / max(1, self.count_tokens(text))
            estimated_end = min(
                current_pos + int(self.config.max_chunk_tokens * estimated_char_per_token),
                len(text)
            )

            # 二分查找精确的token边界
            left, right = current_pos, estimated_end
            while left < right - 1:
                mid = (left + right) // 2
                test_content = text[current_pos:mid]
                test_tokens = self.count_tokens(test_content)
                if test_tokens <= self.config.max_chunk_tokens:
                    left = mid
                else:
                    right = mid
            estimated_end = left

            # 在语义边界处分割
            if estimated_end < len(text):
                # 向前查找合适的分割点
                search_start = max(current_pos + 100, estimated_end - 200)
                for i in range(estimated_end, search_start, -1):
                    if text[i] in "。！？\n\t ":
                        estimated_end = i + 1
                        break

            chunk_content = text[current_pos:estimated_end]
            token_count = self.count_tokens(chunk_content)

            chunks.append(
                TextChunk(
                    content=chunk_content,
                    start_pos=start_pos + current_pos,
                    end_pos=start_pos + estimated_end,
                    chunk_index=chunk_index,
                    token_count=token_count
                )
            )

            # 计算重叠
            overlap_chars = int(self.config.chunk_overlap_tokens * estimated_char_per_token)
            current_pos = (
                estimated_end - overlap_chars if estimated_end < len(text) else estimated_end
            )
            chunk_index += 1

        return chunks

    def merge_results(self, chunk_results: List[Tuple[int, Any]]) -> Any:
        """合并多个块的提取结果

        Args:
            chunk_results: 块结果列表，每个元素为(chunk_index, result)

        Returns:
            合并后的结果
        """
        if not chunk_results:
            return None

        # 按块索引排序
        chunk_results.sort(key=lambda x: x[0])

        # 如果只有一个结果，直接返回
        if len(chunk_results) == 1:
            return chunk_results[0][1]

        # 检查所有结果的类型
        first_result = chunk_results[0][1]

        # 如果第一个结果是字典，尝试合并字典
        if isinstance(first_result, dict):
            return self._merge_dict_results(chunk_results)

        # 如果第一个结果是列表，合并所有列表
        elif isinstance(first_result, list):
            return self._merge_list_results(chunk_results)

        # 对于简单类型（字符串、数字、布尔值），返回第一个非空值
        else:
            return self._merge_simple_results(chunk_results)

    def _merge_dict_results(self, chunk_results: List[Tuple[int, Any]]) -> dict:
        """合并字典类型的结果"""
        merged_result = {}

        for chunk_index, result in chunk_results:
            if not isinstance(result, dict):
                logger.warning(f"块 {chunk_index} 的结果不是字典类型，跳过")
                continue

            for key, value in result.items():
                if key not in merged_result:
                    merged_result[key] = value
                elif isinstance(value, list) and isinstance(merged_result[key], list):
                    # 合并列表
                    merged_result[key].extend(value)
                elif isinstance(value, str) and isinstance(merged_result[key], str):
                    # 合并字符串
                    if value and value not in merged_result[key]:
                        merged_result[key] += f"; {value}"
                elif value is not None and merged_result[key] is None:
                    # 用非空值替换空值
                    merged_result[key] = value

        logger.debug(f"合并 {len(chunk_results)} 个字典结果")
        return merged_result

    def _merge_list_results(self, chunk_results: List[Tuple[int, Any]]) -> list:
        """合并列表类型的结果"""
        merged_list = []

        for chunk_index, result in chunk_results:
            if isinstance(result, list):
                merged_list.extend(result)
            else:
                logger.warning(f"块 {chunk_index} 的结果不是列表类型，跳过")

        logger.debug(f"合并 {len(chunk_results)} 个列表结果，总计 {len(merged_list)} 个元素")
        return merged_list

    def _merge_simple_results(self, chunk_results: List[Tuple[int, Any]]) -> Any:
        """合并简单类型的结果"""
        # 对于简单类型，返回第一个非空值
        for chunk_index, result in chunk_results:
            if result is not None:
                logger.debug(f"使用块 {chunk_index} 的结果作为最终结果")
                return result

        logger.debug("所有块的结果都为空")
        return None
